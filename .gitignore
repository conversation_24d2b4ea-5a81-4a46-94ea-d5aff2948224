# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js
.yarn/install-state.gz

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# local env files
.env*.local

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# vscode
.vscode

# YoYo AI version control directory
.yoyo/

/lib/generated/prisma

# ===================================================================
# Python & FastAPI
# ===================================================================

# 虚拟环境 (Virtual Environments)
# 这是最重要的，避免将整个依赖库上传
.venv/
venv/
env/
ENV/
.env

# Python 缓存与编译文件
__pycache__/
*.pyc
*.pyo
*.pyd

# 打包与分发
# 注意：您原有的 /build 规则会与这里的 build/ 冲突
# build/ 会忽略任何路径下的build目录，更通用，建议保留这个
dist/
build/
*.egg-info/
*.egg
wheels/
parts/
sdist/
var/

# 测试
.pytest_cache/
.coverage
.tox/

# Jupyter Notebook 检查点
.ipynb_checkpoints

# 实例文件夹 (Flask/FastAPI 常见)
/instance