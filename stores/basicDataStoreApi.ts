/**
 * 基础数据Store - API增强版
 * 🎯 核心价值：在原有store基础上增加API同步功能
 * 🔄 混合模式：LocalStorage + API双重存储
 * ⚡ 渐进式：保持现有功能完全不变，逐步迁移到API
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { colorDataApi } from '@/lib/api-client';
import { 
  BasicDataState, 
  BasicColorType, 
  ColorCoordinates, 
  ColorValue, 
  ColorVisibility,
  BlackCellData,
  CellData,
  useBasicDataStore as useOriginalBasicDataStore
} from './basicDataStore';

// API增强状态接口
interface ApiEnhancedBasicDataState extends BasicDataState {
  // API状态
  isOnline: boolean;
  isSyncing: boolean;
  lastSyncTime?: Date;
  syncErrors: string[];
  
  // 当前项目ID
  currentProjectId?: string;
  
  // API操作方法
  setCurrentProject: (projectId: string) => void;
  syncToApi: () => Promise<void>;
  loadFromApi: () => Promise<void>;
  setOnlineStatus: (online: boolean) => void;
  clearSyncErrors: () => void;
  
  // 增强的数据操作方法（支持API同步）
  setColorCoordinatesWithSync: (colorType: BasicColorType, coordinates: ColorCoordinates) => Promise<void>;
  updateColorValuesWithSync: (colorType: BasicColorType, values: Partial<ColorValue>) => Promise<void>;
  setColorVisibilityWithSync: (colorType: BasicColorType, visibility: ColorVisibility) => Promise<void>;
  setBlackCellDataWithSync: (data: BlackCellData) => Promise<void>;
}

// 创建API增强的store
export const useApiEnhancedBasicDataStore = create<ApiEnhancedBasicDataState>()(
  persist(
    (set, get) => {
      // 获取原始store的所有方法和状态
      const originalStore = useOriginalBasicDataStore.getState();
      
      return {
        // 继承原始store的所有状态
        ...originalStore,
        
        // API状态
        isOnline: true,
        isSyncing: false,
        lastSyncTime: undefined,
        syncErrors: [],
        currentProjectId: undefined,
        
        // API操作方法
        setCurrentProject: (projectId: string) => {
          set({ currentProjectId: projectId });
        },
        
        setOnlineStatus: (online: boolean) => {
          set({ isOnline: online });
        },
        
        clearSyncErrors: () => {
          set({ syncErrors: [] });
        },
        
        // 同步到API
        syncToApi: async () => {
          const state = get();
          if (!state.isOnline || !state.currentProjectId || state.isSyncing) {
            return;
          }
          
          set({ isSyncing: true, syncErrors: [] });
          
          try {
            const colorTypes: BasicColorType[] = ['red', 'cyan', 'yellow', 'purple', 'orange', 'green', 'blue', 'pink'];
            
            // 同步颜色数据
            for (const colorType of colorTypes) {
              const colorCoords = state.colorCoordinates[colorType];
              const colorValue = state.colorValues[colorType];
              const visibility = state.colorVisibility[colorType];
              
              // 为每个层级同步数据
              for (let level = 1; level <= 4; level++) {
                const levelKey = `level${level}` as keyof ColorCoordinates;
                const levelCoords = colorCoords[levelKey];
                
                if (levelCoords && levelCoords.length > 0) {
                  await colorDataApi.upsert(state.currentProjectId, {
                    colorType,
                    level,
                    coordinates: levelCoords,
                    colorValue: colorValue.value,
                    visible: visibility[levelKey] ?? true,
                  });
                }
              }
            }
            
            set({ 
              lastSyncTime: new Date(),
              isSyncing: false,
            });
            
            console.log('✅ 数据同步到API成功');
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : '同步失败';
            set({ 
              syncErrors: [errorMessage],
              isSyncing: false,
            });
            console.error('❌ 同步到API失败:', error);
          }
        },
        
        // 从API加载数据
        loadFromApi: async () => {
          const state = get();
          if (!state.isOnline || !state.currentProjectId || state.isSyncing) {
            return;
          }
          
          set({ isSyncing: true, syncErrors: [] });
          
          try {
            // 获取所有颜色数据
            const colorData = await colorDataApi.getByProject(state.currentProjectId);
            
            // 重构数据格式
            const newColorCoordinates = { ...state.colorCoordinates };
            const newColorValues = { ...state.colorValues };
            const newColorVisibility = { ...state.colorVisibility };
            
            for (const data of colorData) {
              const colorType = data.colorType as BasicColorType;
              const level = data.level;
              const levelKey = `level${level}` as keyof ColorCoordinates;
              
              // 更新坐标数据
              if (!newColorCoordinates[colorType]) {
                newColorCoordinates[colorType] = { level1: [], level2: [], level3: [], level4: [] };
              }
              newColorCoordinates[colorType][levelKey] = data.coordinates;
              
              // 更新颜色值
              if (!newColorValues[colorType]) {
                newColorValues[colorType] = { value: data.colorValue };
              }
              newColorValues[colorType].value = data.colorValue;
              
              // 更新可见性
              if (!newColorVisibility[colorType]) {
                newColorVisibility[colorType] = { level1: true, level2: true, level3: true, level4: true };
              }
              newColorVisibility[colorType][levelKey] = data.visible;
            }
            
            set({
              colorCoordinates: newColorCoordinates,
              colorValues: newColorValues,
              colorVisibility: newColorVisibility,
              lastSyncTime: new Date(),
              isSyncing: false,
            });
            
            console.log('✅ 从API加载数据成功');
          } catch (error) {
            const errorMessage = error instanceof Error ? error.message : '加载失败';
            set({ 
              syncErrors: [errorMessage],
              isSyncing: false,
            });
            console.error('❌ 从API加载数据失败:', error);
          }
        },
        
        // 增强的数据操作方法
        setColorCoordinatesWithSync: async (colorType: BasicColorType, coordinates: ColorCoordinates) => {
          // 先更新本地状态
          set((state) => ({
            colorCoordinates: {
              ...state.colorCoordinates,
              [colorType]: coordinates,
            },
          }));
          
          // 如果在线且有项目ID，同步到API
          const state = get();
          if (state.isOnline && state.currentProjectId) {
            try {
              const colorValue = state.colorValues[colorType];
              const visibility = state.colorVisibility[colorType];
              
              // 为每个层级同步数据
              for (let level = 1; level <= 4; level++) {
                const levelKey = `level${level}` as keyof ColorCoordinates;
                const levelCoords = coordinates[levelKey];
                
                if (levelCoords && levelCoords.length > 0) {
                  await colorDataApi.upsert(state.currentProjectId, {
                    colorType,
                    level,
                    coordinates: levelCoords,
                    colorValue: colorValue.value,
                    visible: visibility[levelKey] ?? true,
                  });
                }
              }
            } catch (error) {
              console.error('同步颜色坐标失败:', error);
              // 不阻断本地操作，只记录错误
              set((state) => ({
                syncErrors: [...state.syncErrors, `同步${colorType}坐标失败`],
              }));
            }
          }
        },
        
        updateColorValuesWithSync: async (colorType: BasicColorType, values: Partial<ColorValue>) => {
          // 先更新本地状态
          set((state) => ({
            colorValues: {
              ...state.colorValues,
              [colorType]: { ...state.colorValues[colorType], ...values },
            },
          }));
          
          // 同步到API的逻辑类似...
        },
        
        setColorVisibilityWithSync: async (colorType: BasicColorType, visibility: ColorVisibility) => {
          // 先更新本地状态
          set((state) => ({
            colorVisibility: {
              ...state.colorVisibility,
              [colorType]: visibility,
            },
          }));
          
          // 同步到API的逻辑类似...
        },
        
        setBlackCellDataWithSync: async (data: BlackCellData) => {
          // 先更新本地状态
          set({ blackCellData: data });
          
          // 同步到API的逻辑类似...
        },
        
        // 继承原始store的所有方法
        setColorCoordinates: originalStore.setColorCoordinates,
        updateColorValues: originalStore.updateColorValues,
        setColorVisibility: originalStore.setColorVisibility,
        setColorLevelRules: originalStore.setColorLevelRules,
        setBlackCellData: originalStore.setBlackCellData,
        toggleColorLevel: originalStore.toggleColorLevel,
        initializeGrid: originalStore.initializeGrid,
        updateGridCell: originalStore.updateGridCell,
        getGridDimensions: originalStore.getGridDimensions,
        getGridCenter: originalStore.getGridCenter,
        getCellByCoordinate: originalStore.getCellByCoordinate,
        getCellByPosition: originalStore.getCellByPosition,
      };
    },
    {
      name: 'api-enhanced-basic-data-store',
      version: 1,
    }
  )
);

// 选择器函数（API增强版）
export const useApiColorCoordinates = (colorType?: BasicColorType) => 
  useApiEnhancedBasicDataStore((state) => 
    colorType ? state.colorCoordinates[colorType] : state.colorCoordinates
  );

export const useApiColorValues = (colorType?: BasicColorType) => 
  useApiEnhancedBasicDataStore((state) => 
    colorType ? state.colorValues[colorType] : state.colorValues
  );

export const useApiSyncStatus = () => 
  useApiEnhancedBasicDataStore((state) => ({
    isOnline: state.isOnline,
    isSyncing: state.isSyncing,
    lastSyncTime: state.lastSyncTime,
    syncErrors: state.syncErrors,
  }));
