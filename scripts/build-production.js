/**
 * 生产环境构建脚本
 * 🎯 核心价值：自动化生产环境构建流程
 * 🔄 完整流程：环境配置、数据库迁移、构建验证
 * ⚡ CI/CD友好：适合自动化部署流程
 */

const { execSync } = require('child_process');
const fs = require('fs');

console.log('🚀 开始生产环境构建...\n');

// 运行命令并处理错误
function runCommand(command, description, options = {}) {
  console.log(`🔧 ${description}...`);
  try {
    const result = execSync(command, { 
      stdio: options.silent ? 'pipe' : 'inherit',
      encoding: 'utf8',
      ...options 
    });
    console.log(`✅ ${description}完成\n`);
    return result;
  } catch (error) {
    console.error(`❌ ${description}失败:`);
    if (error.stdout) console.error('STDOUT:', error.stdout);
    if (error.stderr) console.error('STDERR:', error.stderr);
    process.exit(1);
  }
}

// 检查环境变量
function checkEnvironmentVariables() {
  console.log('🔍 检查环境变量...');
  
  const requiredVars = [
    'DATABASE_URL',
    'NEXTAUTH_SECRET',
    'NEXTAUTH_URL'
  ];
  
  const missingVars = requiredVars.filter(varName => !process.env[varName]);
  
  if (missingVars.length > 0) {
    console.error('❌ 缺少必要的环境变量:');
    missingVars.forEach(varName => {
      console.error(`   - ${varName}`);
    });
    console.error('\n请在Vercel或部署平台中设置这些环境变量');
    process.exit(1);
  }
  
  console.log('✅ 环境变量检查通过\n');
}

// 验证数据库连接
function validateDatabaseConnection() {
  console.log('🗄️ 验证数据库连接...');
  
  try {
    // 尝试连接数据库
    runCommand('npx prisma db pull --force', '测试数据库连接', { silent: true });
    console.log('✅ 数据库连接正常\n');
  } catch (error) {
    console.warn('⚠️ 数据库连接测试失败，可能是新数据库');
    console.log('   将在部署时自动创建数据库结构\n');
  }
}

// 运行数据库迁移
function runDatabaseMigration() {
  console.log('📊 运行数据库迁移...');
  
  try {
    // 检查是否有迁移文件
    const migrationsDir = './prisma/migrations';
    if (fs.existsSync(migrationsDir)) {
      runCommand('npx prisma migrate deploy', '应用数据库迁移');
    } else {
      // 如果没有迁移文件，使用db push
      runCommand('npx prisma db push', '同步数据库结构');
    }
  } catch (error) {
    console.warn('⚠️ 数据库迁移失败，将在运行时处理');
  }
}

// 生成Prisma客户端
function generatePrismaClient() {
  runCommand('npx prisma generate', '生成Prisma客户端');
}

// 运行类型检查
function runTypeCheck() {
  runCommand('npm run type-check', 'TypeScript类型检查');
}

// 运行代码检查
function runLinting() {
  try {
    runCommand('npm run lint', '代码质量检查');
  } catch (error) {
    console.warn('⚠️ 代码检查发现问题，但继续构建');
  }
}

// 运行测试
function runTests() {
  try {
    runCommand('npm run test', '运行测试', { silent: true });
  } catch (error) {
    console.warn('⚠️ 测试失败，但继续构建');
  }
}

// 构建应用
function buildApplication() {
  runCommand('npm run build', '构建Next.js应用');
}

// 验证构建结果
function validateBuild() {
  console.log('🧪 验证构建结果...');
  
  const buildDir = './.next';
  if (!fs.existsSync(buildDir)) {
    console.error('❌ 构建目录不存在');
    process.exit(1);
  }
  
  const staticDir = './.next/static';
  if (!fs.existsSync(staticDir)) {
    console.error('❌ 静态资源目录不存在');
    process.exit(1);
  }
  
  console.log('✅ 构建结果验证通过\n');
}

// 生成构建报告
function generateBuildReport() {
  console.log('📋 生成构建报告...');
  
  const buildInfo = {
    timestamp: new Date().toISOString(),
    nodeVersion: process.version,
    environment: process.env.NODE_ENV || 'production',
    vercelEnv: process.env.VERCEL_ENV,
    gitCommit: process.env.VERCEL_GIT_COMMIT_SHA,
    gitBranch: process.env.VERCEL_GIT_COMMIT_REF,
  };
  
  // 获取包信息
  try {
    const packageJson = JSON.parse(fs.readFileSync('./package.json', 'utf8'));
    buildInfo.appVersion = packageJson.version;
    buildInfo.appName = packageJson.name;
  } catch (error) {
    console.warn('⚠️ 无法读取package.json');
  }
  
  // 写入构建信息
  fs.writeFileSync('./public/build-info.json', JSON.stringify(buildInfo, null, 2));
  
  console.log('✅ 构建报告生成完成\n');
  
  // 显示构建信息
  console.log('📊 构建信息:');
  console.log(`   应用: ${buildInfo.appName} v${buildInfo.appVersion}`);
  console.log(`   环境: ${buildInfo.environment}`);
  console.log(`   Node.js: ${buildInfo.nodeVersion}`);
  if (buildInfo.gitCommit) {
    console.log(`   提交: ${buildInfo.gitCommit.substring(0, 8)}`);
  }
  if (buildInfo.gitBranch) {
    console.log(`   分支: ${buildInfo.gitBranch}`);
  }
  console.log('');
}

// 主函数
function main() {
  try {
    // 设置生产环境
    process.env.NODE_ENV = 'production';
    
    console.log('🎯 生产环境构建流程开始\n');
    
    // 1. 环境检查
    checkEnvironmentVariables();
    
    // 2. 数据库相关
    validateDatabaseConnection();
    generatePrismaClient();
    runDatabaseMigration();
    
    // 3. 代码质量检查
    runTypeCheck();
    runLinting();
    runTests();
    
    // 4. 构建应用
    buildApplication();
    
    // 5. 验证和报告
    validateBuild();
    generateBuildReport();
    
    console.log('🎉 生产环境构建完成!');
    console.log('🚀 应用已准备好部署到Vercel\n');
    
  } catch (error) {
    console.error('\n❌ 生产环境构建失败:', error.message);
    process.exit(1);
  }
}

// 运行主函数
if (require.main === module) {
  main();
}

module.exports = { main };
