/**
 * 快速启动脚本 - 一键体验全栈功能
 * 🎯 核心价值：新用户快速上手，展示全栈功能
 * 🔄 完整流程：环境检查、数据库初始化、服务启动、功能演示
 * ⚡ 用户友好：清晰的步骤提示和错误处理
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

console.log('🚀 Cube1_Group 全栈架构快速启动\n');

// 配置
const CONFIG = {
  port: 3000,
  timeout: 30000,
  checkInterval: 1000,
};

// 工具函数
function log(message, type = 'info') {
  const timestamp = new Date().toLocaleTimeString();
  const prefix = {
    info: '📋',
    success: '✅',
    error: '❌',
    warning: '⚠️',
    step: '🔧',
  }[type] || '📋';
  
  console.log(`${prefix} [${timestamp}] ${message}`);
}

function runCommand(command, description) {
  log(`${description}...`, 'step');
  try {
    execSync(command, { stdio: 'inherit' });
    log(`${description}完成`, 'success');
    return true;
  } catch (error) {
    log(`${description}失败: ${error.message}`, 'error');
    return false;
  }
}

// 检查环境
function checkEnvironment() {
  log('检查运行环境...', 'step');
  
  // 检查Node.js版本
  const nodeVersion = process.version;
  const majorVersion = parseInt(nodeVersion.slice(1).split('.')[0]);
  
  if (majorVersion < 18) {
    log(`Node.js版本过低: ${nodeVersion}，需要18+`, 'error');
    return false;
  }
  
  log(`Node.js版本: ${nodeVersion}`, 'success');
  
  // 检查必要文件
  const requiredFiles = [
    'package.json',
    'prisma/schema.prisma',
    'app/page.tsx',
  ];
  
  for (const file of requiredFiles) {
    if (!fs.existsSync(file)) {
      log(`缺少必要文件: ${file}`, 'error');
      return false;
    }
  }
  
  log('环境检查通过', 'success');
  return true;
}

// 安装依赖
function installDependencies() {
  if (fs.existsSync('node_modules')) {
    log('依赖已安装，跳过安装步骤', 'success');
    return true;
  }
  
  return runCommand('npm install', '安装项目依赖');
}

// 设置数据库
function setupDatabase() {
  log('设置数据库...', 'step');
  
  // 确保使用SQLite开发环境
  if (!runCommand('npm run env:dev', '配置开发环境')) {
    return false;
  }
  
  // 生成Prisma客户端
  if (!runCommand('npm run db:generate', '生成数据库客户端')) {
    return false;
  }
  
  // 同步数据库结构
  if (!runCommand('npm run db:push', '同步数据库结构')) {
    return false;
  }
  
  // 初始化种子数据
  if (!runCommand('npm run db:seed', '初始化演示数据')) {
    return false;
  }
  
  log('数据库设置完成', 'success');
  return true;
}

// 启动开发服务器
function startDevServer() {
  log('启动开发服务器...', 'step');
  
  return new Promise((resolve, reject) => {
    const server = spawn('npm', ['run', 'dev'], {
      stdio: ['inherit', 'pipe', 'pipe'],
      shell: true,
    });
    
    let output = '';
    
    server.stdout.on('data', (data) => {
      output += data.toString();
      
      // 检查服务器是否启动成功
      if (output.includes('Ready') || output.includes('started server')) {
        log('开发服务器启动成功', 'success');
        resolve(server);
      }
    });
    
    server.stderr.on('data', (data) => {
      const error = data.toString();
      if (error.includes('Error') || error.includes('EADDRINUSE')) {
        log(`服务器启动失败: ${error}`, 'error');
        reject(new Error(error));
      }
    });
    
    // 超时处理
    setTimeout(() => {
      if (!output.includes('Ready')) {
        log('服务器启动超时', 'error');
        server.kill();
        reject(new Error('启动超时'));
      }
    }, CONFIG.timeout);
  });
}

// 等待服务器就绪
async function waitForServer() {
  log('等待服务器就绪...', 'step');
  
  const maxAttempts = 30;
  let attempts = 0;
  
  while (attempts < maxAttempts) {
    try {
      const fetch = (await import('node-fetch')).default;
      const response = await fetch(`http://localhost:${CONFIG.port}/api/health`);
      
      if (response.ok) {
        log('服务器就绪', 'success');
        return true;
      }
    } catch (error) {
      // 继续等待
    }
    
    attempts++;
    await new Promise(resolve => setTimeout(resolve, CONFIG.checkInterval));
  }
  
  log('服务器就绪检查超时', 'warning');
  return false;
}

// 运行快速测试
async function runQuickTests() {
  log('运行快速功能测试...', 'step');
  
  try {
    const fetch = (await import('node-fetch')).default;
    
    // 测试API健康检查
    const healthResponse = await fetch(`http://localhost:${CONFIG.port}/api/health`);
    if (!healthResponse.ok) {
      log('API健康检查失败', 'error');
      return false;
    }
    
    // 测试用户API
    const userResponse = await fetch(`http://localhost:${CONFIG.port}/api/users`);
    if (!userResponse.ok) {
      log('用户API测试失败', 'error');
      return false;
    }
    
    log('快速功能测试通过', 'success');
    return true;
  } catch (error) {
    log(`功能测试失败: ${error.message}`, 'error');
    return false;
  }
}

// 显示使用指南
function showUsageGuide() {
  console.log('\n' + '='.repeat(60));
  console.log('🎉 Cube1_Group 全栈架构启动成功！');
  console.log('='.repeat(60));
  
  console.log('\n📱 访问应用:');
  console.log(`   🌐 主应用: http://localhost:${CONFIG.port}`);
  console.log(`   🔧 API健康检查: http://localhost:${CONFIG.port}/api/health`);
  console.log(`   📊 数据库管理: 运行 "npm run db:studio"`);
  
  console.log('\n🛠️ 开发工具:');
  console.log('   ⌨️  开发面板: 按 Ctrl+Shift+D');
  console.log('   🧪 API测试: 在开发面板中测试各种API功能');
  console.log('   📝 数据迁移: 测试LocalStorage到数据库的迁移');
  
  console.log('\n🎯 体验功能:');
  console.log('   1. 打开主应用，体验33x33网格系统');
  console.log('   2. 按Ctrl+Shift+D打开开发工具面板');
  console.log('   3. 在开发面板中测试API功能');
  console.log('   4. 运行"npm run db:studio"查看数据库');
  
  console.log('\n📚 有用命令:');
  console.log('   npm run test:integration  # 运行完整集成测试');
  console.log('   npm run db:studio         # 打开数据库管理界面');
  console.log('   npm run env:setup         # 重新配置环境');
  
  console.log('\n🔗 文档链接:');
  console.log('   📖 部署指南: docs/deployment.md');
  console.log('   📋 迁移总结: docs/migration-summary.md');
  console.log('   🏠 项目主页: README.md');
  
  console.log('\n💡 提示:');
  console.log('   - 这是一个AI编程友好的项目');
  console.log('   - 支持LocalStorage和API双重存储');
  console.log('   - 可以无缝迁移现有数据到云端');
  console.log('   - 完整的TypeScript类型安全');
  
  console.log('\n' + '='.repeat(60));
  console.log('🚀 开始探索Cube1_Group的全栈功能吧！');
  console.log('='.repeat(60) + '\n');
}

// 主函数
async function main() {
  try {
    // 环境检查
    if (!checkEnvironment()) {
      process.exit(1);
    }
    
    // 安装依赖
    if (!installDependencies()) {
      process.exit(1);
    }
    
    // 设置数据库
    if (!setupDatabase()) {
      process.exit(1);
    }
    
    // 启动服务器
    const server = await startDevServer();
    
    // 等待服务器就绪
    await waitForServer();
    
    // 运行快速测试
    await runQuickTests();
    
    // 显示使用指南
    showUsageGuide();
    
    // 保持服务器运行
    process.on('SIGINT', () => {
      log('正在关闭服务器...', 'step');
      server.kill();
      process.exit(0);
    });
    
  } catch (error) {
    log(`启动失败: ${error.message}`, 'error');
    process.exit(1);
  }
}

// 运行主函数
if (require.main === module) {
  main();
}

module.exports = { main };
