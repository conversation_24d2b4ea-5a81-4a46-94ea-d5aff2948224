{"name": "cube1-group", "version": 2, "framework": "nextjs", "buildCommand": "npm run build", "outputDirectory": ".next", "installCommand": "npm install", "devCommand": "npm run dev", "env": {"NODE_ENV": "production", "NEXT_PUBLIC_ENV": "production"}, "build": {"env": {"NODE_ENV": "production"}}, "functions": {"app/api/**/*.ts": {"maxDuration": 30}}, "regions": ["hkg1", "sin1"], "github": {"silent": true}}