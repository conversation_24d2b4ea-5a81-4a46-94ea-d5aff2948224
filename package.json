{"name": "@onlook/next-template", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -H 0.0.0.0", "build": "prisma generate && next build", "start": "next start -H 0.0.0.0", "lint": "next lint", "lint:fix": "next lint --fix", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "quality:check": "node scripts/code-quality-monitor.js", "quality:report": "npm run quality:check && open reports/code-quality.md", "format": "prettier --write .", "format:check": "prettier --check .", "analyze": "npm run build && npx @next/bundle-analyzer", "pre-commit": "npm run lint:fix && npm run type-check && npm run test", "ci": "npm run lint && npm run type-check && npm run test:coverage && npm run build", "db:generate": "prisma generate", "db:push": "prisma db push", "db:migrate": "prisma migrate dev", "db:studio": "prisma studio", "db:seed": "tsx prisma/seed.ts", "db:reset": "prisma migrate reset", "postinstall": "prisma generate", "dev:setup": "node scripts/dev-setup.js", "dev:full": "npm run dev:setup && npm run dev", "env:setup": "node scripts/setup-env.js", "env:dev": "node scripts/setup-env.js --env development", "env:prod": "node scripts/setup-env.js --env production", "build:prod": "node scripts/build-production.js", "deploy:vercel": "npm run env:prod && vercel --prod", "test:integration": "node scripts/test-integration.js", "test:api": "npm run test:integration", "test:full": "npm run test && npm run test:integration", "start:quick": "node scripts/quick-start.js", "demo": "npm run start:quick", "cache:clear": "rm -rf .next/cache", "cache:clear-webpack": "rm -rf .next/cache/webpack", "dev:clean": "npm run cache:clear && npm run dev"}, "dependencies": {"@prisma/client": "^6.10.1", "@radix-ui/react-slot": "^1.1.0", "@types/pg": "^8.15.4", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "dotenv": "^17.0.0", "lucide-react": "^0.438.0", "next": "14.2.23", "node-fetch": "^2.7.0", "pg": "^8.16.3", "prisma": "^6.10.1", "react": "^18", "react-dom": "^18", "react-hot-toast": "^2.5.2", "sqlite3": "^5.1.7", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "zustand": "^5.0.5"}, "devDependencies": {"@types/jest": "^30.0.0", "@types/node": "^20", "@types/react": "^18", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "^15.1.6", "jest": "^29.7.0", "postcss": "^8", "prettier": "^3.3.3", "tailwindcss": "^3.4.1", "ts-jest": "^29.4.0", "tsx": "^4.20.3", "typescript": "^5"}}