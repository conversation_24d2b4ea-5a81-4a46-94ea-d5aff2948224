/**
 * 混合数据存储Hook - LocalStorage + API
 * 🎯 核心价值：无缝切换本地存储和API存储，保持功能完全不变
 * 🔄 渐进式迁移：支持逐步从LocalStorage迁移到API
 * ⚡ 离线优先：离线时使用LocalStorage，在线时同步API
 */

import { useEffect, useState, useCallback } from 'react';
import { useBasicDataStore } from '@/stores/basicDataStore';
import { useApiEnhancedBasicDataStore } from '@/stores/basicDataStoreApi';
import { healthApi, migrationApi } from '@/lib/api-client';
import { BasicColorType, ColorCoordinates } from '@/stores';

// 混合模式配置
interface HybridConfig {
  enableApi: boolean;
  autoSync: boolean;
  syncInterval: number; // 毫秒
  offlineMode: boolean;
}

// 混合模式状态
interface HybridState {
  isOnline: boolean;
  isApiAvailable: boolean;
  isMigrated: boolean;
  needsMigration: boolean;
  currentMode: 'localStorage' | 'api' | 'hybrid';
  lastSyncTime?: Date;
  syncErrors: string[];
}

// 默认配置
const DEFAULT_CONFIG: HybridConfig = {
  enableApi: true,
  autoSync: true,
  syncInterval: 30000, // 30秒
  offlineMode: false,
};

export function useHybridDataStore(config: Partial<HybridConfig> = {}) {
  const finalConfig = { ...DEFAULT_CONFIG, ...config };
  
  // 状态管理
  const [hybridState, setHybridState] = useState<HybridState>({
    isOnline: navigator.onLine,
    isApiAvailable: false,
    isMigrated: false,
    needsMigration: false,
    currentMode: 'localStorage',
    syncErrors: [],
  });
  
  // 获取两个store的实例
  const localStore = useBasicDataStore();
  const apiStore = useApiEnhancedBasicDataStore();
  
  // 检查API可用性
  const checkApiAvailability = useCallback(async () => {
    try {
      await healthApi.check();
      setHybridState(prev => ({ 
        ...prev, 
        isApiAvailable: true,
        currentMode: finalConfig.enableApi ? 'hybrid' : 'localStorage'
      }));
      return true;
    } catch (error) {
      setHybridState(prev => ({ 
        ...prev, 
        isApiAvailable: false,
        currentMode: 'localStorage'
      }));
      return false;
    }
  }, [finalConfig.enableApi]);
  
  // 检查是否需要迁移
  const checkMigrationNeed = useCallback(async () => {
    try {
      const result = await migrationApi.checkNeed();
      setHybridState(prev => ({ 
        ...prev, 
        needsMigration: result.needsMigration 
      }));
      return result.needsMigration;
    } catch (error) {
      console.error('检查迁移需求失败:', error);
      return false;
    }
  }, []);
  
  // 执行数据迁移
  const performMigration = useCallback(async (userId: string) => {
    try {
      setHybridState(prev => ({ ...prev, syncErrors: [] }));
      
      const result = await migrationApi.migrate({ userId });
      
      if (result.isCompleted) {
        setHybridState(prev => ({ 
          ...prev, 
          isMigrated: true,
          needsMigration: false,
          lastSyncTime: new Date(),
        }));
        
        // 设置API store的项目ID（假设迁移后有默认项目）
        // 这里需要根据实际迁移结果设置
        console.log('✅ 数据迁移完成');
        return true;
      } else {
        setHybridState(prev => ({ 
          ...prev, 
          syncErrors: result.errors 
        }));
        return false;
      }
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '迁移失败';
      setHybridState(prev => ({ 
        ...prev, 
        syncErrors: [errorMessage] 
      }));
      console.error('数据迁移失败:', error);
      return false;
    }
  }, []);
  
  // 同步数据到API
  const syncToApi = useCallback(async () => {
    if (!hybridState.isApiAvailable || !finalConfig.enableApi) {
      return;
    }
    
    try {
      await apiStore.syncToApi();
      setHybridState(prev => ({ 
        ...prev, 
        lastSyncTime: new Date(),
        syncErrors: []
      }));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '同步失败';
      setHybridState(prev => ({ 
        ...prev, 
        syncErrors: [errorMessage] 
      }));
    }
  }, [hybridState.isApiAvailable, finalConfig.enableApi, apiStore]);
  
  // 从API加载数据
  const loadFromApi = useCallback(async () => {
    if (!hybridState.isApiAvailable || !finalConfig.enableApi) {
      return;
    }
    
    try {
      await apiStore.loadFromApi();
      setHybridState(prev => ({ 
        ...prev, 
        lastSyncTime: new Date(),
        syncErrors: []
      }));
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : '加载失败';
      setHybridState(prev => ({ 
        ...prev, 
        syncErrors: [errorMessage] 
      }));
    }
  }, [hybridState.isApiAvailable, finalConfig.enableApi, apiStore]);
  
  // 智能数据操作：根据当前模式选择合适的store
  const setColorCoordinates = useCallback(async (
    colorType: BasicColorType, 
    coordinates: ColorCoordinates
  ) => {
    if (hybridState.currentMode === 'api' || hybridState.currentMode === 'hybrid') {
      // 使用API增强的方法
      await apiStore.setColorCoordinatesWithSync(colorType, coordinates);
    } else {
      // 使用本地存储方法
      localStore.setColorCoordinates(colorType, coordinates);
    }
  }, [hybridState.currentMode, apiStore, localStore]);
  
  // 获取当前有效的数据
  const getCurrentData = useCallback(() => {
    if (hybridState.currentMode === 'api') {
      return {
        colorCoordinates: apiStore.colorCoordinates,
        colorValues: apiStore.colorValues,
        colorVisibility: apiStore.colorVisibility,
        gridData: apiStore.gridData,
        blackCellData: apiStore.blackCellData,
      };
    } else {
      return {
        colorCoordinates: localStore.colorCoordinates,
        colorValues: localStore.colorValues,
        colorVisibility: localStore.colorVisibility,
        gridData: localStore.gridData,
        blackCellData: localStore.blackCellData,
      };
    }
  }, [hybridState.currentMode, apiStore, localStore]);
  
  // 网络状态监听
  useEffect(() => {
    const handleOnline = () => {
      setHybridState(prev => ({ ...prev, isOnline: true }));
      checkApiAvailability();
    };
    
    const handleOffline = () => {
      setHybridState(prev => ({ 
        ...prev, 
        isOnline: false,
        currentMode: 'localStorage'
      }));
    };
    
    window.addEventListener('online', handleOnline);
    window.addEventListener('offline', handleOffline);
    
    return () => {
      window.removeEventListener('online', handleOnline);
      window.removeEventListener('offline', handleOffline);
    };
  }, [checkApiAvailability]);
  
  // 初始化检查
  useEffect(() => {
    const initialize = async () => {
      if (hybridState.isOnline && finalConfig.enableApi) {
        const apiAvailable = await checkApiAvailability();
        if (apiAvailable) {
          await checkMigrationNeed();
        }
      }
    };
    
    initialize();
  }, [hybridState.isOnline, finalConfig.enableApi, checkApiAvailability, checkMigrationNeed]);
  
  // 自动同步
  useEffect(() => {
    if (!finalConfig.autoSync || !hybridState.isApiAvailable) {
      return;
    }
    
    const interval = setInterval(() => {
      if (hybridState.currentMode === 'hybrid') {
        syncToApi();
      }
    }, finalConfig.syncInterval);
    
    return () => clearInterval(interval);
  }, [finalConfig.autoSync, finalConfig.syncInterval, hybridState.isApiAvailable, hybridState.currentMode, syncToApi]);
  
  return {
    // 状态
    hybridState,
    
    // 数据访问
    data: getCurrentData(),
    
    // 操作方法
    setColorCoordinates,
    
    // 管理方法
    performMigration,
    syncToApi,
    loadFromApi,
    checkApiAvailability,
    
    // 配置
    config: finalConfig,
    
    // 原始store访问（用于特殊情况）
    localStore,
    apiStore,
  };
}
