import { useMemo, useCallback, useEffect } from 'react';
import type { CellData } from '../types/grid';
import type { ColorType } from '../stores';
import type { ControlPanelContainerProps } from '../components/ControlPanel/types';

// 新Store架构导入
import { useBasicDataStore } from '../stores/basicDataStore';
import { useDynamicStyleStore } from '../stores/dynamicStyleStore';
import { useBusinessDataStore } from '../stores/businessDataStore';
import { useStyleStore } from '../stores/styleStore';

import { ColorCoordinateIndex } from '../utils/colorSystem';
import { usePerformanceOptimized } from './useFormHandlers';

export const usePageLogic = () => {
    // 0. 性能优化hooks
    const {
        colorPriority,
        colorMap,
        baseClasses,
        defaultStyles,
        circleScaleConfig,
        getEffectiveColor,
        isLevelVisible,
        buildCellClasses,
        getCellKey,
        ensureSetType,
        checkLevelExists,
        debugCellRenderPath,
    } = usePerformanceOptimized();

    // 1. 从新Store架构获取状态和操作
    
    // 基础数据：网格数据、颜色坐标和可见性
    const basicDataStore = useBasicDataStore();
    const { 
        gridData, 
        initializeGrid,
        colorCoordinates,
        setColorCoordinates,
        colorVisibility,
        setColorVisibility,
        colorLevelRules,
        blackCellData,
        setBlackCellData,
        toggleColorLevel,
    } = basicDataStore;
    
    // 动态样式数据
    const dynamicStyleStore = useDynamicStyleStore();
    const {
        fontSize,
        matrixMargin,
        cellShape,
        displayMode,
        enableCircleScale,
        circleScaleFactor,
        showAllNumbers,
        showAllColors,
        showAllLevel1,
        showAllLevel2,
        showAllLevel3,
        showAllLevel4,
        setFontSize,
        setMatrixMargin,
        setCellShape,
        setDisplayMode,
        toggleCircleScale,
        setCircleScaleFactor,
        setShowAllNumbers,
        setShowAllColors,
        setShowAllLevel1,
        setShowAllLevel2,
        setShowAllLevel3,
        setShowAllLevel4,
    } = dynamicStyleStore;

    // 业务数据：交互状态、UI状态、版本管理
    const businessDataStore = useBusinessDataStore();
    const {
        clickedCells,
        handleCellClick,
        clearAllClickedCells,
        interactionState,
        setHoverInfo,
        activePanel,
        settingsSubPage,
        singleColorMode,
        showSpecificGroup,
        setActivePanel,
        setSettingsSubPage,
        setSingleColorMode,
        setShowSpecificGroup,
        showToastNotification,
        toastState,
        // 版本管理
        defaultVersions,
        currentDefaultVersion,
        newDefaultVersionName,
        groupModeVersions,
        currentGroupModeVersion,
        newGroupModeVersionName,
        mixedModeVersions,
        currentMixedModeVersion,
        newMixedModeVersionName,
        saveVersion,
        switchToVersion,
        deleteVersion,
        setNewVersionName,
    } = businessDataStore;

    // 🔧 黑色格子显示状态同步 - 使用blackCellData.visibility替代showBlackCells
    const showBlackCells = blackCellData.visibility;

    // 样式数据：CSS映射（移除showBlackCells相关）
    const styleStore = useStyleStore();
    const { 
        getColorCSSMap, 
        getBlackCSS 
    } = styleStore;

    // 2. 初始化网格数据
    useEffect(() => {
        initializeGrid();
    }, [initializeGrid]);
    
    // 2.1 Debug Phase-4 修复: 挂载stores到window对象以便debugHelper访问
    useEffect(() => {
        if (typeof window !== 'undefined') {
            (window as any).stores = {
                basicData: {
                    gridData,
                    colorCoordinates,
                    colorVisibility,
                    colorLevelRules: true, // 模拟数据
                },
                business: {
                    clickedCells,
                    interactionState,
                    showSpecificGroup,
                    defaultVersions,
                    currentDefaultVersion,
                },
                dynamic: {
                    fontSize,
                    matrixMargin,
                    cellShape,
                    displayMode,
                    enableCircleScale,
                    circleScaleFactor,
                },
                style: {
                    showBlackCells,
                    colorCSSMap: getColorCSSMap,
                },
                // 为了兼容现有的debugHelper，也添加combination别名
                combination: {
                    selectedGroups: new Map(), // 模拟数据
                    modeActivation: false, // 模拟数据
                }
            };
            
            console.log('🔗 Stores已挂载到window对象，debugHelper现在可以访问stores状态');
        }
    }, [gridData, colorCoordinates, colorVisibility, clickedCells, interactionState, 
        showSpecificGroup, defaultVersions, currentDefaultVersion, fontSize, matrixMargin, 
        cellShape, displayMode, enableCircleScale, circleScaleFactor, showBlackCells, getColorCSSMap]);
    
    // 2.2 清理可能损坏的localStorage数据（一次性运行）
    useEffect(() => {
        try {
            // 检查并清理可能损坏的business-data-store数据
            const storedData = localStorage.getItem('business-data-store');
            if (storedData) {
                const parsed = JSON.parse(storedData);
                if (parsed.state && parsed.state.clickedCells && !(parsed.state.clickedCells instanceof Array)) {
                    console.warn('发现损坏的localStorage数据，正在清理...');
                    localStorage.removeItem('business-data-store');
                    window.location.reload(); // 重新加载页面以获取干净的状态
                }
            }
        } catch (error) {
            console.warn('清理localStorage数据时出错:', error);
            localStorage.removeItem('business-data-store');
        }
    }, []); // 只在组件首次挂载时运行

    // 🔧 构建黑色格子坐标Map
    const specialCoordinates = useMemo(() => {
        const coordsMap = new Map<string, string>();
        blackCellData.coordinates.forEach(({ coords, letter }) => {
            const [x, y] = coords;
            coordsMap.set(`${x},${y}`, letter);
        });
        return coordsMap;
    }, [blackCellData.coordinates]);

    // 3. 创建颜色索引（记忆化）- 🔧 修复：传递specialCoordinates
    const colorIndex = useMemo(() => {
        return new ColorCoordinateIndex(colorCoordinates, specialCoordinates);
    }, [colorCoordinates, specialCoordinates]);

    // 4. 核心渲染函数（使用useCallback和性能优化）- Debug Phase-1.2 增强调试
    const getCellStyle = useCallback((cell: CellData): string => {
        const { x, y } = cell;
        const coordKey = getCellKey(x, y);
        
        // 使用优化的Set类型检查
        const safeClickedCells = ensureSetType(clickedCells);
        const isClicked = safeClickedCells.has(coordKey);
        const allColorInfo = colorIndex.getAllColorInfo(x, y);

        // Debug Phase-1.2: 调试特定坐标
        debugCellRenderPath(x, y, allColorInfo, 'getCellStyle');

        let bgColor = defaultStyles.bgColor;
        let textColor = defaultStyles.textColor;
        let borderColor = defaultStyles.borderColor;

        // 🚀 Debug Phase-2.1: 修复红色格子渲染逻辑 - 优化颜色优先级处理
        // 首先处理有颜色的格子（红色、青色等），黑色格子作为兜底
        const effectiveColor = getEffectiveColor(allColorInfo, colorPriority);

        if (singleColorMode && (!effectiveColor || effectiveColor.type !== singleColorMode)) {
            bgColor = defaultStyles.grayBg;
            textColor = defaultStyles.grayText;
            borderColor = defaultStyles.grayBorder;
        } else if (effectiveColor) {
            // 处理有效颜色格子（包括红色1级）
            const { type, level, group } = effectiveColor;
            const visibility = colorVisibility[type as keyof typeof colorVisibility];
            
            // Debug-3.2: 更新级别可见性检查调用，传递colorType参数
            const debugContext = { coords: coordKey };
            const levelVisible = isLevelVisible(visibility, level, type, debugContext);
            const isGroupVisible = showSpecificGroup === null || group === showSpecificGroup;

            // Debug Phase-6: 极简调试模式 - 移除格子级别的冗余输出
            const isCompactDebug = typeof window !== 'undefined' ? sessionStorage.getItem('debug-compact-mode') === 'true' : true;
            const debugFilter = typeof window !== 'undefined' ? sessionStorage.getItem('debug-coords-filter') : null;
            
            // 🚀 Phase-6 简约化：只在以下严格条件下输出调试信息
            const shouldLog = !isCompactDebug && (
                (debugFilter && coordKey === debugFilter) ||  // 用户明确指定的坐标
                (type === 'red' && level === 1 && !levelVisible)  // 红色1级异常（关键问题）
            );
            
            if (shouldLog) {
                console.log(`🎨 调试坐标 ${coordKey}:`, {
                    colorType: type,
                    level,
                    levelVisible,
                    isGroupVisible,
                    reason: !levelVisible ? '级别不可见' : !isGroupVisible ? '分组不可见' : '正常'
                });
            }

            if ((visibility?.showCells !== false && levelVisible && isGroupVisible) || isClicked) {
                const levelClassKey = `level${level}` as 'level1' | 'level2' | 'level3' | 'level4';
                const colorCSS = getColorCSSMap(type as ColorType);
                const levelBgColor = colorCSS?.[levelClassKey];
                bgColor = levelBgColor || colorCSS?.bg || 'bg-gray-500';
                textColor = 'text-white';
                borderColor = 'border-transparent';
            } else {
                // 🚀 Phase-6 简约化：只记录红色1级关键异常
                if (!isCompactDebug && type === 'red' && level === 1 && !levelVisible) {
                    console.warn(`❌ 红色1级异常 ${coordKey}: level不可见`);
                }
            }
        } else if (allColorInfo.black) {
            // 兜底处理：仅当没有其他颜色时才显示黑色格子
            if (showBlackCells || isClicked) {
                bgColor = getBlackCSS('bg') || 'bg-black';
                textColor = 'text-white';
                borderColor = 'border-transparent';
            }
        }

        // 使用优化的CSS类构建函数
        return buildCellClasses(baseClasses, cellShape, bgColor, textColor, borderColor, isClicked, defaultStyles);
    }, [clickedCells, colorIndex, cellShape, showBlackCells, getBlackCSS, singleColorMode, colorVisibility, showSpecificGroup, getColorCSSMap, getCellKey, ensureSetType, defaultStyles, getEffectiveColor, colorPriority, isLevelVisible, buildCellClasses, baseClasses, debugCellRenderPath]);

    // 5. 圆形缩放样式（使用useCallback和性能优化）
    const getCircleScaleStyle = useCallback((cell: CellData): React.CSSProperties => {
        if (!enableCircleScale) return {};
        
        const allColorInfo = colorIndex.getAllColorInfo(cell.x, cell.y);
        const { black, ...colors } = allColorInfo;

        if (black && showBlackCells) {
             return {
                transform: `scale(${circleScaleFactor})`,
                ...circleScaleConfig,
            };
        }

        for (const color in colors) {
            const colorInfo = colors[color as ColorType];
            if (colorInfo && colorInfo.level === 1) {
                const visibility = colorVisibility[color as keyof typeof colorVisibility];
                // 🚀 修复：正确调用isLevelVisible函数，特别针对红色一级
                const levelVisible = isLevelVisible(visibility, 1, color);
                if ((visibility?.showCells !== false && levelVisible)) {
                    return {
                        transform: `scale(${circleScaleFactor})`,
                        ...circleScaleConfig,
                    };
                }
            }
        }

        return {};
    }, [enableCircleScale, circleScaleFactor, colorIndex, showBlackCells, colorVisibility, circleScaleConfig, isLevelVisible]);

    // 6. 格子内容计算函数（使用useCallback和性能优化）
    const getCellContent = useCallback((cell: CellData) => {
        const { x, y, number } = cell;
        const allColorInfo = colorIndex.getAllColorInfo(x, y);

        if (showAllNumbers) {
            if (allColorInfo.black) return allColorInfo.black.letter;
            
            // 使用优化的颜色查找
            for (const color of colorPriority) {
                if (allColorInfo[color]) return colorMap[color];
            }
            return '';
        }

        if (displayMode === 'hidden') return '';
        if (displayMode === 'coordinate') return `${x},${y}`;
        
        // 🚀 Debug Phase-2.1: 修复红色格子内容显示逻辑 - 优化颜色优先级处理
        // 首先处理有颜色的格子（红色、青色等），黑色格子作为兜底
        const effectiveColor = getEffectiveColor(allColorInfo, colorPriority);

        if (singleColorMode && (!effectiveColor || effectiveColor.type !== singleColorMode)) return '';

        if (effectiveColor) {
            const { type, level, group } = effectiveColor;
            const visibility = colorVisibility[type as keyof typeof colorVisibility];
            const levelVisible = isLevelVisible(visibility, level, type);
            const isGroupVisible = showSpecificGroup === null || group === showSpecificGroup;

            if ((visibility?.showCells !== false && levelVisible && isGroupVisible)) {
                if (displayMode === 'number') return number.toString();
                return group?.toString() ?? '';
            }
        } else if (allColorInfo.black) {
            // 兜底处理：仅当没有其他颜色时才显示黑色格子内容
            if (showBlackCells) {
                if (displayMode === 'number') return number.toString();
                return allColorInfo.black.letter;
            }
        }

        return '';
    }, [colorIndex, showAllNumbers, displayMode, showBlackCells, singleColorMode, colorVisibility, showSpecificGroup, colorPriority, colorMap, getEffectiveColor, isLevelVisible]);

    // 7. 处理格子点击（使用useCallback优化性能）
    const onCellClick = useCallback((cell: CellData) => {
        const cellKey = `${cell.x},${cell.y}`;
        handleCellClick(cellKey);
    }, [handleCellClick]);

    // 8. 处理悬停信息的适配器函数（使用useCallback优化性能）
    const handleHoverInfo = useCallback((info: string) => {
        setHoverInfo({
            x: 0,
            y: 0,
            content: info
        });
    }, [setHoverInfo]);

    // 9. Tab样式函数（使用useCallback优化性能）
    const getTabStyle = useCallback((tabKey: string, isActive: boolean) => {
        return `px-4 py-2 text-sm font-semibold border-b-2 transition-colors duration-200 ${
            isActive ? 'border-blue-500 text-white' : 'border-transparent text-gray-400 hover:text-white'
        }`;
    }, []);

    // 10. R2面板架构：如果是首次访问，默认显示R2样式面板
    const effectiveActivePanel = activePanel || 'r2-style';

    // 11. 构建控制面板Props（记忆化优化）
    const controlPanelProps: ControlPanelContainerProps = useMemo(() => ({
        activePanel: effectiveActivePanel,
        setActivePanel: (panel) => setActivePanel(panel as any),
        getTabStyle,
        versionProps: {
            currentVersion: currentDefaultVersion,
            onVersionChange: (version) => {
                const versionData = defaultVersions[version];
                if (versionData && versionData.data) {
                    // 恢复颜色坐标
                    if (versionData.data.colorCoordinates) {
                        Object.entries(versionData.data.colorCoordinates).forEach(([color, coordinates]) => {
                            setColorCoordinates(color as keyof typeof colorCoordinates, coordinates as any);
                        });
                    }
                    // 恢复颜色可见性
                    if (versionData.data.colorVisibility) {
                        Object.entries(versionData.data.colorVisibility).forEach(([color, visibility]) => {
                            setColorVisibility(color as keyof typeof colorVisibility, visibility as any);
                        });
                    }
                }
                switchToVersion('default', version);
            },
            versions: Object.keys(defaultVersions).map(name => ({
                id: name,
                name,
                description: defaultVersions[name]?.description || ''
            })),
            onSaveVersion: () => {
                if (newDefaultVersionName.trim()) {
                    saveVersion('default', newDefaultVersionName, {
                        colorCoordinates,
                        colorVisibility
                    });
                    showToastNotification(`版本 "${newDefaultVersionName}" 已保存`, 'success');
                }
            },
            onDeleteVersion: (versionName) => {
                deleteVersion('default', versionName);
                showToastNotification(`版本 "${versionName}" 已删除`, 'success');
            },
            onExportData: () => {
                const dataStr = JSON.stringify(defaultVersions, null, 2);
                const blob = new Blob([dataStr], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = `版本数据_${new Date().toISOString().split('T')[0]}.json`;
                link.click();
                URL.revokeObjectURL(url);
                showToastNotification('数据导出成功！', 'success');
            },
            onImportData: (event) => {
                const file = event.target.files?.[0];
                if (!file) return;
                const reader = new FileReader();
                reader.onload = (e) => {
                    try {
                        const data = JSON.parse(e.target?.result as string);
                        showToastNotification('数据导入成功！', 'success');
                    } catch (error) {
                        showToastNotification('导入失败，请检查文件格式！', 'error');
                    }
                };
                reader.readAsText(file);
                event.target.value = '';
            },
        },
    }), [
        effectiveActivePanel, setActivePanel, getTabStyle, currentDefaultVersion, defaultVersions, 
        newDefaultVersionName, colorCoordinates, colorVisibility, setColorCoordinates, setColorVisibility, 
        switchToVersion, saveVersion, deleteVersion, showToastNotification
    ]);

    // 返回所有页面需要的状态和函数
    return {
        // 网格数据
        gridData,
        colorIndex,
        specialCoordinates,
        
        // 样式配置
        fontSize,
        matrixMargin,
        cellShape,
        displayMode,
        
        // 核心渲染函数
        getCellStyle,
        getCellContent,
        getCircleScaleStyle,
        
        // 事件处理函数
        onCellClick,
        handleHoverInfo,
        
        // UI状态
        interactionState,
        toastState,
        effectiveActivePanel,
        
        // 组件Props
        controlPanelProps,
        
        // Toast状态解构
        showToast: toastState.show,
        toastMessage: toastState.message,
        toastType: toastState.type,
    };
}; 